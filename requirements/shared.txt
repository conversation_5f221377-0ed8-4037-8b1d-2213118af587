# configuration
pydantic_settings


# data manipulation and mathematics
numpy
pandas
scipy

# deep and machine learning
torch
scikit-learn
nltk

# document ingestion
pytesseract             # OCR
tesserocr
unstructured[all-docs]  # document loaders
docx2txt
pymupdf

PyPDF2>=2.0.0           # metadata, PDF documents
Pillow>=8.0.0           # metadata, image manipulation
python-docx>=0.8.10     # metadata, Microsoft Word (.docx)
openpyxl>=3.0.0         # metadata, Microsoft Excel (.xlsx)
python-pptx>=0.6.0      # metadata, Microsoft PowerPoint (.pptx)
PyYAML>=5.4.0           # metadata, YAML, structured data
tomli>=1.2.0            # metadata, TOML, structured data
odfpy>=1.4.0            # metadata, OpenDocument Format (.odt, .ods)
beautifulsoup4>=4.9.0   # metadata, HTML & XML parsing
chardet>=4.0.0          # metadata, character encoding detection
tiktoken>=0.3.0         # metadata, token counting (for LLMs)

sentence-transformers   # embedding model
rank-bm25               # document indexer for retrival
transformers            # tokenizer, reranking model

# vector database
faiss-cpu               # faiss
weaviate-client         # weaviate
langchain-community     # chroma + document loaders
