# configuration
pydantic_settings
# data manipulation and mathematics
numpy
pandas
scipy
# deep and machine learning
torch~=2.6.0
scikit-learn
nltk
# document ingestion
pytesseract                    # OCR
tesserocr
unstructured[all-docs]         # document loaders
docx2txt
pymupdf
sentence-transformers~=4.1     # embedding model
rank-bm25~=0.2.1               # document indexer for retrival
transformers                   # tokenizer, reranking model
# vector database
faiss-cpu~=1.10                # faiss
weaviate-client                # weaviate
langchain-community            # chroma + document loaders
